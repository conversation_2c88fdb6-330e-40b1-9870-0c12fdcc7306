package com.tech.ekvayu.Adapter

import android.R
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.Response.Results
import com.tech.ekvayu.databinding.ItemSpamMailBinding


class SpamMailAdapter(private val items: List<Results>,  val listener: OnSpamMailClickListener) : RecyclerView.Adapter<SpamMailAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemSpamMailBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemSpamMailBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>uViewHolder, position: Int) {
        val item = items[position]

        // Set sender and receiver emails with cleaner formatting
        holder.binding.tvSender.text = item.sendersEmail
        holder.binding.tvReceiver.text = "To: ${item.recieversEmail}"

        // Set a placeholder date/time (you can replace this with actual timestamp from your data)
        holder.binding.tvDateTime.text = "Recently"

        // Handle status with improved styling - spam mails are typically "unsafe"
        when (item.status) {
            "unsafe" -> {
                holder.binding.tvStatus.text = "Unsafe"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.red))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            "safe" -> {
                holder.binding.tvStatus.text = "Safe"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.green))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            else -> {
                holder.binding.tvStatus.text = "Pending"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.orange))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
        }

        holder.binding.root.setOnClickListener {
            listener.onSpamClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  OnSpamMailClickListener{
        fun onSpamClicked(item: Results)
    }
}
