package com.tech.ekvayu.EkService

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.regex.Pattern

class GmailAccessbilityService : AccessibilityService() {

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private val apiService: ApiService by lazy { RetrofitClient.getInstance().create(ApiService::class.java) }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    try {
                        val root = rootInActiveWindow
                        if (root != null) extractEmailDetails(root)
                    } catch (e: Exception) {
                        Log.e("GmailAccessibility", "Error in service: ${e.message}")
                    }
                }, 500) // 0.5-second delay
            }
        }
    }

    override fun onInterrupt() {
        Log.d("GmailAccessibility", "Service interrupted")
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d("GmailAccessibility", "Gmail Accessibility Service connected")

        val info = AccessibilityServiceInfo()
        info.eventTypes = AccessibilityEvent.TYPE_VIEW_CLICKED
        info.packageNames = arrayOf("com.google.android.gm")
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS
        serviceInfo = info
    }

    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        var senderName: String? = null
        var senderEmail: String? = null
        var receiverEmail: String? = null
        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            Log.d("NodeScan", "Text: $text | ViewId: $viewId | Desc: $contentDesc")
            val actualText = text ?: contentDesc ?: continue

            // Extract Subject
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
                Log.d("EmailExtraction", "📧 Found subject: $subject")
            }

            // Extract Sender (Name + Email)
            if (senderEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if (parts.size == 2) {
                    senderName = parts[0]
                    senderEmail = parts[1].extractEmail()
                    Log.d("EmailExtraction", "📧 Found sender: $senderName <$senderEmail>")
                }
            }

            // Extract sender email from context
            if (senderEmail == null && (viewId?.contains("sender", true) == true ||
                        viewId?.contains("from", true) == true ||
                        contentDesc?.contains("sender", true) == true ||
                        contentDesc?.contains("from", true) == true) &&
                actualText.isValidEmail()) {
                senderEmail = actualText
                Log.d("EmailExtraction", "📧 Found sender email via context: $senderEmail")
            }

            // Extract sender name
            if (senderName == null && actualText.startsWith("From:", true)) {
                val nameText = actualText.removePrefix("From:").trim()
                if (nameText.isNotEmpty() && !nameText.contains("@")) {
                    senderName = nameText
                    Log.d("EmailExtraction", "📧 Found sender name: $senderName")
                }
            }

            // Extract To emails
            if (actualText.startsWith("To:", true)) {
                val extracted = actualText.extractEmails()
                if (extracted.isNotEmpty()) {
                    toEmails.addAll(extracted)
                    Log.d("EmailExtraction", "📧 Found To emails: ${extracted.joinToString(", ")}")
                } else if (actualText.contains("me", true) || actualText.contains("yourself", true)) {
                    // Self-email case
                    if (senderEmail != null && senderEmail.isValidEmail()) {
                        toEmails.add(senderEmail)
                        Log.d("EmailExtraction", "📧 Added self email to To: $senderEmail")
                    }
                }
            }

            // Extract CC emails
            if (actualText.startsWith("Cc:", true)) {
                val extracted = actualText.extractEmails()
                ccEmails.addAll(extracted)
                Log.d("EmailExtraction", "📧 Found CC emails: ${extracted.joinToString(", ")}")
            }

            // Extract BCC emails
            if (actualText.startsWith("Bcc:", true)) {
                val extracted = actualText.extractEmails()
                bccEmails.addAll(extracted)
                Log.d("EmailExtraction", "📧 Found BCC emails: ${extracted.joinToString(", ")}")
            }

            // Extract Date
            if (date == null && (actualText.contains("AM") || actualText.contains("PM") ||
                        actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")))) {
                date = actualText
                Log.d("EmailExtraction", "📧 Found date: $date")
            }

            // Extract Body content
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true) &&
                !actualText.startsWith("Subject", true)) {
                emailBodyBuilder.appendLine(actualText)
            }

            // Extract Attachments
            if (attachments == null && (actualText.contains("Attachment", true) ||
                        actualText.contains(".pdf", true) ||
                        actualText.contains(".docx", true) ||
                        actualText.contains(".jpg", true) ||
                        actualText.contains(".png", true) ||
                        actualText.contains("Download", true))) {
                attachments = "Attachments found"
                Log.d("EmailExtraction", "📧 Found attachments: $attachments")
            }

            // Extract any valid email addresses
            if (actualText.isValidEmail() && !toEmails.contains(actualText) &&
                !ccEmails.contains(actualText) && !bccEmails.contains(actualText) &&
                actualText != senderEmail) {

                val hasRecipientContext = viewId?.let { id ->
                    id.contains("recipient", true) ||
                    id.contains("to", true) ||
                    id.contains("addressee", true)
                } ?: false || contentDesc?.let { desc ->
                    desc.contains("recipient", true) ||
                    desc.contains("to", true) ||
                    desc.contains("sent to", true)
                } ?: false

                if (hasRecipientContext) {
                    toEmails.add(actualText)
                    Log.d("EmailExtraction", "📧 Found recipient email via context: $actualText")
                }
            }
        }

        // Set fallback values
        subject = subject ?: "No Subject"
        senderEmail = senderEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        attachments = attachments ?: "No Attachments"

        // Determine receiver email using smart detection
        if (senderEmail != "Unknown Sender") {
            receiverEmail = EmailDetectionHelper.determineReceiverEmail(
                fromEmail = senderEmail,
                toEmails = toEmails.toList(),
                ccEmails = ccEmails.toList(),
                bccEmails = bccEmails.toList(),
                context = applicationContext,
                isForwardedEmail = false
            )
        } else {
            receiverEmail = if (toEmails.isNotEmpty()) toEmails[0] else "Unknown Receiver"
        }

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        Log.d("EmailDetails", """
            📧 EXTRACTED EMAIL DATA:
            senderName: $senderName
            senderEmail: $senderEmail
            receiverEmail: $receiverEmail
            toEmails: ${toEmails.joinToString(", ")}
            ccEmails: ${ccEmails.joinToString(", ")}
            bccEmails: ${bccEmails.joinToString(", ")}
            subject: $subject
            date: $date
            deviceId: $androidId
            body: $body
            attachments: $attachments
        """.trimIndent())

        // Create EML file content
        val emailContent = """
            From: $senderName <$senderEmail>
            To: ${toEmails.joinToString(", ")}
            Cc: ${ccEmails.joinToString(", ")}
            Bcc: ${bccEmails.joinToString(", ")}
            Subject: $subject
            Date: $date
            Content-Type: text/plain; charset=UTF-8

            $body

            Attachments: $attachments
        """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        Log.d("EmailFile", "EML file path: ${file.absolutePath}")

        try {
            val fos = FileOutputStream(file)
            fos.write(emailContent.toByteArray())
            fos.close()
            Log.d("EmailFile", "EML file created successfully")
        } catch (e: IOException) {
            Log.e("EmailFile", "Error creating EML file: ${e.message}")
        }

        // Process the email if we have valid data
        if (toEmails.isNotEmpty() && senderEmail != "Unknown Sender") {
            sharedPrefManager.putString(AppConstant.receiverMail, receiverEmail)
            processEmailData(file, receiverEmail, senderEmail, ccEmails, bccEmails, subject, date, body, attachments)
        } else {
            Log.w("EmailProcessing", "Cannot process email: Missing required data")
        }
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo, result: MutableList<AccessibilityNodeInfo>) {
        if (node.text != null || node.contentDescription != null) {
            result.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, result)
                child.recycle()
            }
        }
    }

    private fun processEmailData(
        emlFile: File,
        receiverEmail: String,
        senderEmail: String,
        ccEmails: List<String>,
        bccEmails: List<String>,
        subject: String,
        date: String,
        body: String,
        attachments: String
    ) {
        Log.d("EmailProcessing", "Processing email data for receiver: $receiverEmail")

        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlFile)
        val bodyReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), body)
        val ccReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), ccEmails.joinToString(", "))
        val bccReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), bccEmails.joinToString(", "))
        val subjectReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), subject)

        // Try upload file first
        apiService.uploadFile(fileRequestBody, senderEmail, bodyReqBody, ccReqBody, bccReqBody, subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(call: Call<EmailResponse>, response: Response<EmailResponse>) {
                    if (response.isSuccessful && response.body()?.Code == 1) {
                        val hashId = response.body()?.hashId.toString()
                        sharedPrefManager.putString(AppConstant.hashId, hashId)
                        sharedPrefManager.putString(AppConstant.receiverMail, receiverEmail)
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)

                        Log.d("EmailProcessing", "✅ Email uploaded successfully. HashId: $hashId")
                        checkAiResponse(receiverEmail, hashId)
                    } else {
                        Log.w("EmailProcessing", "Upload failed, trying hash key method")
                        // Fallback to hash key method
                        getHashId(emlFile, receiverEmail, senderEmail, ccEmails, bccEmails, subject, date, body, attachments)
                    }
                }

                override fun onFailure(call: Call<EmailResponse>, t: Throwable) {
                    Log.e("EmailProcessing", "Upload failed: ${t.message}")
                    // Fallback to hash key method
                    getHashId(emlFile, receiverEmail, senderEmail, ccEmails, bccEmails, subject, date, body, attachments)
                }
            })
    }

    private fun getHashId(
        emlFile: File,
        receiverEmail: String,
        senderEmail: String,
        ccEmails: List<String>,
        bccEmails: List<String>,
        subject: String,
        date: String,
        body: String,
        attachments: String
    ) {
        Log.d("EmailProcessing", "Getting hash ID for email")

        val bodyReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), body)
        val ccReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), ccEmails.joinToString(", "))
        val bccReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), bccEmails.joinToString(", "))
        val subjectReqBody = RequestBody.create("text/plain".toMediaTypeOrNull(), subject)

        apiService.getHashKey(receiverEmail, senderEmail, bodyReqBody, ccReqBody, bccReqBody, subjectReqBody)
            .enqueue(object : Callback<HashResponse> {
                override fun onResponse(call: Call<HashResponse>, response: Response<HashResponse>) {
                    if (response.isSuccessful && response.body()?.Code.toString() == "1") {
                        val hashId = response.body()?.hashId.toString()
                        sharedPrefManager.putString(AppConstant.hashId, hashId)
                        sharedPrefManager.putString(AppConstant.receiverMail, receiverEmail)
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)

                        Log.d("EmailProcessing", "✅ Hash key obtained successfully. HashId: $hashId")
                        checkAiResponse(receiverEmail, hashId)
                    } else {
                        Log.e("EmailProcessing", "Failed to get hash key: ${response.body()?.Code}")
                        Toast.makeText(applicationContext, "Failed to process email", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                    Log.e("EmailProcessing", "Hash key request failed: ${t.message}")
                    Toast.makeText(applicationContext, "Network error occurred", Toast.LENGTH_SHORT).show()
                }
            })
    }

    private fun checkAiResponse(email: String, hashId: String, retryCount: Int = 1) {
        if (retryCount > 10) {
            Log.w("EmailProcessing", "Max retry attempts reached for AI response")
            Toast.makeText(applicationContext, "Unable to get response. Try again later.", Toast.LENGTH_LONG).show()
            return
        }

        Log.d("EmailProcessing", "Checking AI response - Attempt: $retryCount")

        val request = PendingMailRequest(
            CommonRequest(
                sharedPrefManager.getString(AppConstant.authtoken, ""),
                Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
            ),
            hashId
        )

        apiService.pendingMailStatusAi(request)
            .enqueue(object : Callback<PedingMailRes> {
                override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                    val data = response.body()?.data
                    val status = data?.emlStatus?.lowercase()?.trim()

                    Log.d("EmailProcessing", "AI Response - Attempt $retryCount: status=$status")

                    when (status) {
                        "safe", "unsafe" -> {
                            Log.d("EmailProcessing", "✅ Final status received: $status")
                            Toast.makeText(
                                applicationContext,
                                "Email processed: $status",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        "pending" -> {
                            if (retryCount < 10) {
                                // Retry after 3 seconds
                                Handler(Looper.getMainLooper()).postDelayed({
                                    checkAiResponse(email, hashId, retryCount + 1)
                                }, 3000)
                            } else {
                                Toast.makeText(
                                    applicationContext,
                                    "Unable to get response. Try again later.",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        }
                        else -> {
                            Log.w("EmailProcessing", "Unknown status: $status")
                            if (retryCount < 10) {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    checkAiResponse(email, hashId, retryCount + 1)
                                }, 3000)
                            }
                        }
                    }
                }

                override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                    Log.e("EmailProcessing", "AI response check failed: ${t.message}")
                    if (retryCount < 10) {
                        Handler(Looper.getMainLooper()).postDelayed({
                            checkAiResponse(email, hashId, retryCount + 1)
                        }, 3000)
                    } else {
                        Toast.makeText(
                            applicationContext,
                            "Network error occurred",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            })
    }

    // Extension functions for email validation and extraction
    private fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    private fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    private fun String.extractEmail(): String {
        val emailPattern = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
        val matcher = emailPattern.matcher(this)
        return if (matcher.find()) matcher.group() else this
    }
}