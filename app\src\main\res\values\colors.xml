<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base Colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="transparent">#00000000</color>

    <!-- App Primary Colors -->
    <color name="app_color">#FF000080</color>
    <color name="app_color_light">#FF4040B0</color>
    <color name="app_color_dark">#FF000060</color>

    <!-- Status Colors -->
    <color name="red">#FF1100</color>
    <color name="green">#046A08</color>
    <color name="orange">#FF8C00</color>
    <color name="blue">#0066CC</color>

    <!-- Semantic Colors -->
    <color name="success">#28A745</color>
    <color name="success_light">#D4EDDA</color>
    <color name="warning">#FFC107</color>
    <color name="warning_light">#FFF3CD</color>
    <color name="info">#17A2B8</color>
    <color name="info_light">#D1ECF1</color>
    <color name="error">#DC3545</color>
    <color name="error_light">#F8D7DA</color>

    <!-- Light Theme Colors -->
    <color name="background_light">#FFFFFF</color>
    <color name="surface_light">#F8F9FA</color>
    <color name="card_background_light">#FFFFFF</color>
    <color name="text_primary_light">#212529</color>
    <color name="text_secondary_light">#6C757D</color>
    <color name="divider_light">#E9ECEF</color>
    <color name="border_light">#DEE2E6</color>

    <!-- Dark Theme Colors -->
    <color name="background_dark">#121212</color>
    <color name="surface_dark">#1E1E1E</color>
    <color name="card_background_dark">#2D2D2D</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_secondary_dark">#B3B3B3</color>
    <color name="divider_dark">#404040</color>
    <color name="border_dark">#505050</color>

    <!-- Legacy Colors (for backward compatibility) -->
    <color name="transparent_black">#CC000000</color>
    <color name="transparent_black_60">#99000000</color>
    <color name="grey">#DCDCDC</color>
    <color name="light_grey">#E9E9E9</color>
    <color name="dark_grey">#6B6A6A</color>

    <!-- Theme Adaptive Colors (Light Mode) -->
    <color name="background">@color/background_light</color>
    <color name="surface">@color/surface_light</color>
    <color name="card_background">@color/card_background_light</color>
    <color name="text_primary">@color/text_primary_light</color>
    <color name="text_secondary">@color/text_secondary_light</color>
    <color name="divider">@color/divider_light</color>
    <color name="border">@color/border_light</color>
</resources>