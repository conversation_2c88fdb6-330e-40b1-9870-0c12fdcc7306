package com.tech.ekvayu.Adapter

import android.R
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.Dispute.Results
import com.tech.ekvayu.databinding.ItemDisputeListBinding



class DisputeListAdapter(private val items: List<Results>, val listener: OnDisputeMailListClickListener) : RecyclerView.Adapter<DisputeListAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemDisputeListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemDisputeListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>uV<PERSON>wHolder, position: Int) {
        val item = items[position]

        // Set sender and receiver emails with cleaner formatting
        holder.binding.tvSender.text = item.sendersEmail
        holder.binding.tvReceiver.text = "To: ${item.recieversEmail}"

        // Set a placeholder date/time (you can replace this with actual timestamp from your data)
        holder.binding.tvDateTime.text = "Recently"

        // Handle status with improved styling
        when (item.status) {
            "safe" -> {
                holder.binding.tvStatus.text = "Safe"
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.holo_green_dark))
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.green))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            "unsafe" -> {
                holder.binding.tvStatus.text = "Unsafe"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.red))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
            else -> {
                holder.binding.tvStatus.text = "Pending"
                holder.binding.cvStatusBadge.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, com.tech.ekvayu.R.color.orange))
                holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            }
        }

        holder.binding.root.setOnClickListener {
            listener.onDisputeClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  OnDisputeMailListClickListener{
        fun onDisputeClicked(item: Results)
    }
}
