<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".Activities.DashboardActivity">


    <include
        android:id="@+id/header"
        layout="@layout/header_layout"/>


    <FrameLayout
        app:layout_constraintTop_toBottomOf="@+id/header"
        android:id="@+id/fragmentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_10sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        />




</androidx.constraintlayout.widget.ConstraintLayout>
