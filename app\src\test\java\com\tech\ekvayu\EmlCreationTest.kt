package com.tech.ekvayu

import org.junit.Test
import org.junit.Assert.*

/**
 * Test for EML file creation functionality
 */
class EmlCreationTest {

    @Test
    fun testEmlContentGeneration() {
        // Sample email data
        val emailData = EmailData(
            senderName = "<PERSON>",
            fromEmail = "<EMAIL>",
            toEmails = setOf("<EMAIL>"),
            ccEmails = setOf("<EMAIL>"),
            bccEmails = setOf(),
            subject = "Test Email Subject",
            date = "Mon, 01 Jan 2024 12:00:00 +0000",
            body = "This is a test email body content.",
            attachments = "document.pdf"
        )

        // Expected EML content structure
        val expectedHeaders = listOf(
            "From: <PERSON> <<EMAIL>>",
            "To: <EMAIL>",
            "Cc: <EMAIL>",
            "Subject: Test Email Subject",
            "Date: Mon, 01 Jan 2024 12:00:00 +0000",
            "MIME-Version: 1.0",
            "Content-Type: text/plain; charset=UTF-8"
        )

        // Test that email data is properly structured
        assertEquals("<EMAIL>", emailData.fromEmail)
        assertEquals("Test Email Subject", emailData.subject)
        assertEquals(1, emailData.toEmails.size)
        assertTrue(emailData.toEmails.contains("<EMAIL>"))
        
        println("✅ EML creation test passed")
    }

    @Test
    fun testFilenameGeneration() {
        val subject = "Re: Important Meeting - Follow Up (2024)"
        val cleanSubject = subject.replace(Regex("[^a-zA-Z0-9\\s]"), "")
            .replace(Regex("\\s+"), "_")
            .take(50)
        
        assertEquals("Re_Important_Meeting_Follow_Up_2024", cleanSubject)
        println("✅ Filename generation test passed")
    }
}

// Data class for testing (should match the one in the service)
data class EmailData(
    val senderName: String?,
    val fromEmail: String,
    val toEmails: Set<String>,
    val ccEmails: Set<String>,
    val bccEmails: Set<String>,
    val subject: String,
    val date: String,
    val body: String,
    val attachments: String
)
