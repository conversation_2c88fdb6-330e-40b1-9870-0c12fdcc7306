package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class NewGmailAccessibilityService : AccessibilityService() {

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    try {
                        val root = rootInActiveWindow
                        if (root != null) extractEmailDetails(root)

                    } catch (e: Exception) {
                        Log.e("AccessibilityCrash", "Error in service: ${e.message}")
                    }


                }, 500) // 0.5 -second delay
            }
        }
    }

   /* private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {

        var senderName: String? = null
        var senderMail: String? = null
        var receiverMail: String? = null

        var subject: String? = null
        var dateTime: String? = null
        var attachments: String? = null
        val attachmentSet = mutableSetOf<String>()
        val emailBodyBuilder = StringBuilder()
        val urlSet = mutableSetOf<String>()
        val contentBuilder = StringBuilder()

        val toEmails = mutableSetOf<String>()
        val ccEmails = mutableSetOf<String>()
        val bccEmails = mutableSetOf<String>()

        var isForwardedEmail = false
        val originalToEmails = mutableSetOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            if (actualText.isValidEmail()) {
                Log.d("EmailDetection", "📧 FOUND VALID EMAIL: '$actualText'")
                Log.d("EmailDetection", "   ViewId: '$viewId'")
                Log.d("EmailDetection", "   ContentDesc: '$contentDesc'")

                val couldBeRecipient = viewId?.contains("to", true) == true ||
                                     viewId?.contains("recipient", true) == true ||
                                     contentDesc?.contains("to", true) == true ||
                                     contentDesc?.contains("recipient", true) == true ||
                                     contentDesc?.contains("sent to", true) == true

                if (couldBeRecipient) {
                    Log.d("EmailDetection", "🎯 POTENTIAL RECIPIENT: '$actualText' (Context match)")
                } else {
                    Log.d("EmailDetection", "❓ UNKNOWN EMAIL CONTEXT: '$actualText'")
                }

                // Check if it's the sender
                if (senderMail != null && actualText.equals(senderMail, ignoreCase = true)) {
                    Log.d("EmailDetection", "👤 SENDER EMAIL: '$actualText' (will skip as recipient)")
                }
            }

            if (actualText.isNotEmpty() && actualText.length > 3) {
                contentBuilder.appendLine(actualText)
            }

            if (actualText.contains("forwarded", true) ||
                actualText.contains("fwd:", true) ||
                actualText.startsWith("---------- Forwarded message", true) ||
                actualText.contains("Begin forwarded message", true)) {
                isForwardedEmail = true
                Log.d("ForwardedEmail", "📧 Detected forwarded email pattern: $actualText")
            }

            if (subject == null) {
                if (viewId?.contains("subject", true) == true ||
                    actualText.startsWith("Subject", true) ||
                    viewId?.contains("conversation_subject", true) == true ||
                    contentDesc?.contains("subject", true) == true) {
                    subject = actualText.removePrefix("Subject:").removePrefix("Re:").removePrefix("Fwd:").trim()
                    Log.d("SubjectExtraction", "📧 Found subject: $subject")

                    if (actualText.startsWith("Fwd:", true) || actualText.startsWith("FW:", true)) {
                        isForwardedEmail = true
                        Log.d("ForwardedEmail", "📧 Detected forwarded email from subject: $actualText")
                    }
                }
            }

            if (senderName == null || senderMail == null) {
                if (actualText.contains("•")) {
                    val parts = actualText.split("•").map { it.trim() }
                    if (parts.size == 2) {
                        senderName = parts[0]
                        senderMail = parts[1].extractEmail()
                        Log.d("SenderExtraction", "📧 Found sender (•): $senderName <$senderMail>")
                    }
                }
                else if (actualText.contains("<") && actualText.contains(">")) {
                    val emailMatch = Regex("<([^>]+)>").find(actualText)
                    if (emailMatch != null) {
                        senderMail = emailMatch.groupValues[1].trim()
                        senderName = actualText.replace(emailMatch.value, "").trim()
                        Log.d("SenderExtraction", "📧 Found sender (<>): $senderName <$senderMail>")
                    }
                }
                else if ((viewId?.contains("sender", true) == true ||
                         viewId?.contains("from", true) == true ||
                         contentDesc?.contains("sender", true) == true ||
                         contentDesc?.contains("from", true) == true) &&
                         actualText.isValidEmail()) {
                    senderMail = actualText
                    Log.d("SenderExtraction", "📧 Found sender email via context: $senderMail")
                }
                else if (actualText.startsWith("From:", true) ||
                         (viewId?.contains("sender_name", true) == true && !actualText.contains("@"))) {
                    val nameText = actualText.removePrefix("From:").trim()
                    if (senderName == null && nameText.isNotEmpty() && !nameText.contains("@")) {
                        senderName = nameText
                        Log.d("SenderExtraction", "📧 Found sender name: $senderName")
                    }
                }
            }

            if (actualText.startsWith("To:", true)) {
                val extracted = actualText.extractEmails()
                if (extracted.isNotEmpty()) {
                    toEmails.addAll(extracted)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 1 - Found To emails from 'To:': ${extracted.joinToString(", ")}")
                } else if (actualText.contains("me", true) || actualText.contains("yourself", true)) {
                    // Fallback: Assume sending to self, so use senderMail
                    if (senderMail != null && senderMail.isValidEmail()) {
                        toEmails.add(senderMail)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 1 - Added self email to To: $senderMail")
                    }
                }
            }

            val isGmailRecipientViewId = viewId?.let { id ->
                id.contains("to", true) ||
                id.contains("recipient", true) ||
                id.contains("addressee", true) ||
                id.contains("compose_to", true) ||
                id.contains("send_to", true) ||
                id.endsWith("_to", true) ||
                id.contains("email_to", true)
            } ?: false

            if (isGmailRecipientViewId && actualText.isValidEmail()) {
                val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                if (!isSender && !toEmails.contains(actualText)) {
                    toEmails.add(actualText)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 2 - Found recipient via ViewId: '$actualText' (ViewId: '$viewId')")
                }
            }

            val isRecipientContentDesc = contentDesc?.let { desc ->
                desc.contains("to ", true) ||
                desc.contains("recipient", true) ||
                desc.contains("sent to", true) ||
                desc.contains("addressee", true) ||
                desc.contains("deliver to", true)
            } ?: false

            if (isRecipientContentDesc && actualText.isValidEmail()) {
                val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                if (!isSender && !toEmails.contains(actualText)) {
                    toEmails.add(actualText)
                    Log.d("ToEmailExtraction", "✅ STRATEGY 3 - Found recipient via ContentDesc: '$actualText' (Desc: '$contentDesc')")
                }
            }

            if (actualText.isValidEmail()) {
                val couldBeRecipientByPosition = text?.let { nodeText ->
                    val fullText = nodeText.lowercase()
                    fullText.contains("to:") ||
                    fullText.contains("sent to") ||
                    fullText.contains("deliver to") ||
                    fullText.contains("recipient:")
                } ?: false

                if (couldBeRecipientByPosition) {
                    val isSender = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                    if (!isSender && !toEmails.contains(actualText)) {
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 4 - Found recipient via position context: '$actualText'")
                    }
                }
            }

            if (isForwardedEmail && (actualText.contains("Original Message", true) ||
                actualText.contains("From:", true) || actualText.contains("To:", true))) {
                val forwardedEmails = actualText.extractEmails()
                if (forwardedEmails.isNotEmpty()) {
                    originalToEmails.addAll(forwardedEmails)
                    Log.d("ForwardedEmail", "📧 Found original emails in forwarded content: ${forwardedEmails.joinToString(", ")}")
                }
            }
            if (viewId?.contains("to", true) == true && actualText.contains("@")) {
                val extractedEmails = actualText.extractEmails()
                toEmails.addAll(extractedEmails)
                Log.d("ToEmailExtraction", "📧 Found hidden To emails via viewId: ${extractedEmails.joinToString(", ")}")
            }
            if (actualText.isValidEmail()) {
                val isSenderEmail = senderMail != null && actualText.equals(senderMail, ignoreCase = true)
                if (!isSenderEmail &&
                    !toEmails.contains(actualText) &&
                    !ccEmails.contains(actualText) &&
                    !bccEmails.contains(actualText)) {

                    val hasRecipientContext = viewId?.let { id ->
                        id.contains("recipient", true) ||
                        id.contains("to", true) ||
                        id.contains("addressee", true)
                    } ?: false || contentDesc?.let { desc ->
                        desc.contains("recipient", true) ||
                        desc.contains("to", true) ||
                        desc.contains("sent to", true)
                    } ?: false

                    if (hasRecipientContext) {
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "✅ STRATEGY 5A - Added email with recipient context: '$actualText' (ViewId: '$viewId', Desc: '$contentDesc')")
                    } else if (toEmails.isEmpty()) {
                        // Only add as fallback if we have no recipients yet
                        toEmails.add(actualText)
                        Log.d("ToEmailExtraction", "⚠️ STRATEGY 5B - Added email as fallback recipient: '$actualText' (no other recipients found)")
                    } else {
                        Log.d("ToEmailExtraction", "❌ SKIPPED - Email without recipient context: '$actualText' (already have ${toEmails.size} recipients)")
                    }
                } else if (isSenderEmail) {
                    Log.d("ToEmailExtraction", "👤 SKIPPED - Sender email: '$actualText'")
                } else {
                    Log.d("ToEmailExtraction", "🔄 SKIPPED - Already categorized email: '$actualText'")
                }
            }

            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }
            if (dateTime == null) {
                val isDateTime = actualText.contains("AM", true) ||
                               actualText.contains("PM", true) ||
                               actualText.matches(Regex(".*\\d{1,2}[:/]\\d{1,2}[:/]\\d{2,4}.*")) ||
                               actualText.matches(Regex(".*\\d{1,2} [A-Za-z]{3,9} \\d{4}.*")) ||
                               actualText.matches(Regex(".*\\d{4}-\\d{2}-\\d{2}.*")) ||
                               actualText.matches(Regex(".*[A-Za-z]{3,9} \\d{1,2}, \\d{4}.*")) ||
                               viewId?.contains("date", true) == true ||
                               viewId?.contains("time", true) == true ||
                               contentDesc?.contains("date", true) == true ||
                               contentDesc?.contains("time", true) == true

                if (isDateTime) {
                    dateTime = actualText
                    Log.d("DateTimeExtraction", "📅 Found datetime: $dateTime")
                }
            }

            val isBodyContent = actualText.length > 20 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true) &&
                !actualText.startsWith("From", true) &&
                !actualText.startsWith("Subject", true) &&
                !actualText.startsWith("Date", true) &&
                !actualText.contains("AM", true) &&
                !actualText.contains("PM", true) &&
                !actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")) &&
                viewId?.contains("body", true) != false &&
                viewId?.contains("content", true) != false

            if (isBodyContent ||
                viewId?.contains("message_body", true) == true ||
                viewId?.contains("email_body", true) == true ||
                viewId?.contains("conversation_body", true) == true ||
                contentDesc?.contains("message body", true) == true) {
                emailBodyBuilder.appendLine(actualText)
                Log.d("BodyExtraction", "📄 Added to body: ${actualText.take(50)}...")
            }

        }

        if (isForwardedEmail) {

            if (toEmails.isEmpty() && originalToEmails.isNotEmpty()) {
                toEmails.addAll(originalToEmails)
                Log.d("ForwardedEmail", "📧 Using original To emails from forwarded content: ${originalToEmails.joinToString(", ")}")
            }

            if (toEmails.isEmpty() && senderMail != null && senderMail.isValidEmail()) {
                toEmails.add(senderMail)
                Log.d("ForwardedEmail", "📧 Using sender as recipient for forwarded email: $senderMail")
            }
        }

        Log.d("EmailDetectionSummary", """
            📧 ===== EMAIL DETECTION COMPLETE =====
            Total nodes scanned: ${possibleNodes.size}

            📧 RESULTS:
            ✅ To Emails: ${toEmails.size} - [${toEmails.joinToString(", ")}]
            📧 CC Emails: ${ccEmails.size} - [${ccEmails.joinToString(", ")}]
            📧 BCC Emails: ${bccEmails.size} - [${bccEmails.joinToString(", ")}]
            👤 Sender: $senderMail
            📅 DateTime: $dateTime
            🔗 URLs: ${urlSet.size}
            📎 Attachments: ${attachments}
            🔄 Is Forwarded: $isForwardedEmail

            📧 DETECTION STATUS:
            ${if (toEmails.isNotEmpty()) "✅ Recipients found successfully" else "❌ NO RECIPIENTS DETECTED"}
            ${if (senderMail != null) "✅ Sender detected" else "❌ No sender detected"}
            =====================================
        """.trimIndent())

        Log.d("AttachmentExtraction", "📎 Current attachment count before final scan: ${attachmentSet.size}")
        Log.d("AttachmentExtraction", "📎 Current attachments: [${attachmentSet.joinToString(", ")}]")

        Log.d("AttachmentExtraction", "📎 Performing aggressive attachment scan for additional attachments...")
        val additionalAttachments = performAggressiveAttachmentScan(possibleNodes)
        if (additionalAttachments.isNotEmpty()) {
            val newAttachments = additionalAttachments.filter { !attachmentSet.contains(it) }
            if (newAttachments.isNotEmpty()) {
                attachmentSet.addAll(newAttachments)
                Log.d("AttachmentExtraction", "📎 Found ${newAttachments.size} additional attachments: ${newAttachments.joinToString(", ")}")
            }
        }

        Log.d("ReceiverDetection", """
            📧 RECEIVER DETECTION STATE:
            toEmails found: ${toEmails.size} - ${toEmails.joinToString(", ")}
            ccEmails found: ${ccEmails.size} - ${ccEmails.joinToString(", ")}
            bccEmails found: ${bccEmails.size} - ${bccEmails.joinToString(", ")}
            Initial receiverMail: $receiverMail
            senderMail: $senderMail
        """.trimIndent())

        Log.d("EmailExtraction", """
            📧 COMPREHENSIVE EMAIL EXTRACTION SUMMARY:
            ==========================================
            Sender Name: $senderName
            Sender Mail: $senderMail
            Receiver Mail: $receiverMail
            Subject: $subject
            DateTime: $dateTime
            Is Forwarded: $isForwardedEmail
            To Emails: ${toEmails.joinToString(", ")}
            CC Emails: ${ccEmails.joinToString(", ")}
            BCC Emails: ${bccEmails.joinToString(", ")}
            URLs Found: ${urlSet.size} - ${urlSet.joinToString(", ")}
            Attachments: ${attachmentSet.size} - ${attachmentSet.joinToString(", ")}
            Body Length: ${emailBodyBuilder.length} characters
            Total Content Length: ${contentBuilder.length} characters
        """.trimIndent())

        // 📧 Enhanced Fallbacks with better defaults
        subject = subject?.takeIf { it.isNotEmpty() } ?: "No Subject"
        senderMail = senderMail?.takeIf { it.isNotEmpty() } ?: "Unknown Sender"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        val urls = if (urlSet.isNotEmpty()) urlSet.joinToString(", ") else "No URLs"
        val content = if (contentBuilder.isNotEmpty()) contentBuilder.toString().trim() else "No Content"

        Log.d("AttachmentFinalization", """
            📎 ===== ATTACHMENT FINALIZATION =====
            Raw attachment set size: ${attachmentSet.size}
            Raw attachments: [${attachmentSet.joinToString(" | ")}]
            =====================================
        """.trimIndent())

        val cleanedAttachments = attachmentSet
            .filter { it.isNotEmpty() && it.trim().length > 1 }
            .map { it.trim() }
            .toSet()

        Log.d("AttachmentFinalization", "📎 Cleaned attachments: [${cleanedAttachments.joinToString(" | ")}]")

        attachments = if (cleanedAttachments.isNotEmpty()) {
            val attachmentList = cleanedAttachments.joinToString(", ")
            val finalResult = "Attachments (${cleanedAttachments.size}): $attachmentList"
            Log.d("AttachmentFinalization", "✅ Final attachment result: $finalResult")
            finalResult
        } else {
            Log.w("AttachmentFinalization", "❌ No valid attachments found - returning 'No Attachments'")
            "No Attachments"
        }

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        val emailContent = """
        From: $senderName <$senderMail>
        To: ${toEmails.joinToString(", ")}
        Cc: ${ccEmails.joinToString(", ")}
        Bcc: ${bccEmails.joinToString(", ")}
        Subject: $subject
        Date: $dateTime
        MIME-Version: 1.0
        Content-Type: text/plain; charset=UTF-8
        X-Forwarded: $isForwardedEmail
        X-URLs: $urls
        X-Attachments: ${attachmentSet.size}
        X-Content-Length: ${content.length}

        $body

        --- EXTRACTED URLS ---
        $urls

        --- FULL CONTENT ---
        $content
    """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        try {
            FileOutputStream(file).use { it.write(emailContent.toByteArray()) }
            readEmlFile(file.absolutePath)?.let {
                Log.d("getEmailContent", "saveEmailAsEml: $it")
            }
        } catch (e: IOException) {
            Log.e("FileWriteError", "Failed to write eml file", e)
        }

        // 📧 Enhanced automatic receiver email detection
        if (senderMail != "Unknown Sender") {
            val automaticReceiverEmail = EmailDetectionHelper.determineReceiverEmail(
                fromEmail = senderMail,
                toEmails = toEmails.toList(),
                ccEmails = ccEmails.toList(),
                bccEmails = bccEmails.toList(),
                context = applicationContext,
                isForwardedEmail = isForwardedEmail
            )

            // Update receiverMail with the automatically determined email
            receiverMail = automaticReceiverEmail

            Log.d("EmailProcessing", "🤖 AUTOMATIC receiver email determined: $automaticReceiverEmail")
            Log.d("EmailProcessing", "📧 Email type: ${if (isForwardedEmail) "FORWARDED" else "REGULAR"}")
            Log.d("EmailProcessing", "📧 Final receiver mail: $receiverMail")

        } else {
            Log.w("EmailProcessing", "Cannot process email: Unknown sender")
        }

      //  getHashId(file, automaticReceiverEmail, fromEmail, ccEmails.toList(), bccEmails.toList(), subject, date, body, attachments)

    }
*/


    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        var senderName: String? = null
        var fromEmail: String? = null

        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {

            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            Log.d("NodeScan", "Text: $text | ViewId: $viewId | Desc: $contentDesc")
            val actualText = text ?: contentDesc ?: continue

            // Subject
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
            }

            // From (Name + Email)
            if (fromEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if ( parts.size == 2) {
                    senderName = parts[0]
                    fromEmail = parts[1].extractEmail()
                }
            }

            if ((viewId?.contains("to", true) == true || actualText.contains("To:", true)) && actualText.contains("@")) {
                toEmails.addAll(actualText.extractEmails())
            }

            if (actualText.isValidEmail() && !toEmails.contains(actualText)) {
                toEmails.add(actualText)
            }

            if (viewId?.endsWith("/to") == true) {
                toEmails.addAll(actualText.extractEmails())
            }

            if (actualText.contains("@")) {
                Log.d("EmailCheck", "Possible email text: $actualText from $viewId")
            }

            // To
            if (actualText.startsWith("To:", true)) {
                toEmails.addAll(actualText.extractEmails())
            }

            // Cc
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            // Bcc
            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // Date
            if (date == null && (
                        actualText.contains("AM") || actualText.contains("PM") ||
                                actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*"))
                        )
            ) {
                date = actualText
            }

            // Body
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true)
            ) {
                emailBodyBuilder.appendLine(actualText)
            }

            // Attachments
            if (attachments == null && (
                        actualText.contains("Attachment", true) ||
                                actualText.contains(".pdf", true) ||
                                actualText.contains(".docx", true) ||
                                actualText.contains("Download", true))
            ) {
                attachments = "Attachments found"
            }

        }

        // Fallbacks
        subject = subject ?: "No Subject"
        fromEmail = fromEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        attachments = attachments ?: "No Attachments"

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        Log.d("EmailDetails", """
        senderName: $senderName
        fromEmail: $fromEmail
        toEmail: ${toEmails.joinToString(", ")}
        cc: ${ccEmails.joinToString(", ")}
        bcc: ${bccEmails.joinToString(", ")}
        subject: $subject
        date: $date
        deviceId: $androidId
        body: $body
        attachments: $attachments
    """.trimIndent())


        val emailContent = """
                   fromEmail: $fromEmail
                   toEmail: ${toEmails.joinToString(", ")}
                   cc: ${ccEmails.joinToString(", ")}
                   bcc: ${bccEmails.joinToString(", ")}
                   subject: $subject
                   date: $date
                   body: $body
                   attachments: $attachments
                   Content-Type: text/plain; charset=UTF-8 
               """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")

        Log.d("getFilePath", "emlFile: " + file)

        try {
            val fos = FileOutputStream(file)
            fos.write(emailContent.toByteArray())
            fos.close()
            val emlContent = readEmlFile(file.absolutePath)
            if (emlContent != null) {
                Log.d("getEmailContnettt", "saveEmailAsEml: " + emlContent)
            }
        } catch (e: IOException) {
        }



        if (toEmails[0].isNotEmpty() && (fromEmail!= "Unknown Sender"))
        {
            // Log.d("getAllRightData", "receiverMail: "+toEmails[1].toString()+" senderMail "+fromEmail+" cc "+ccEmails+" bcc "+bccEmails+" subject "+ subject +" date "+date+" body "+body+" attachments "+attachments+"")
            sharedPrefManager.putString(AppConstant.receiverMail,toEmails[0].toString())
            getHashId(file,toEmails[0].toString(),fromEmail,ccEmails,bccEmails,subject,date,body,attachments)
        }

    }

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    private fun performAggressiveAttachmentScan(nodes: List<AccessibilityNodeInfo>): List<String> {
        val foundAttachments = mutableSetOf<String>()

        Log.d("AggressiveAttachmentScan", "📎 Starting AGGRESSIVE attachment scan across ${nodes.size} nodes")

        for (node in nodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()
            val className = node.className?.toString()

            // Get all possible text sources
            val allTexts = listOfNotNull(text, contentDesc).filter { it.isNotEmpty() }

            for (actualText in allTexts) {
                if (actualText.length < 1 || actualText.length > 300) continue

                Log.d("AggressiveAttachmentScan", "📎 AGGRESSIVE CHECK: '$actualText' | ViewId: '$viewId'")

                // 🎯 ULTRA-AGGRESSIVE PATTERN 1: Any file extension anywhere
                val ultraFilePattern = Regex(
                    """\b[\w\-\s().\[\]]*\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip|rar|7z|tar|gz|bz2|xz|jpg|jpeg|png|gif|bmp|svg|webp|tiff|tif|mp4|avi|mov|wmv|flv|mkv|webm|mp3|wav|flac|aac|ogg|m4a|ppt|xls|doc|rtf|html|xml|json|apk|exe|dmg|iso|bin|deb|rpm|msi|app|ipa|torrent|sql|db|log|cfg|ini|bat|sh|py|js|css|php|java|cpp|c|h|swift|kt|go|rs|rb|pl|lua|r|m|scala|clj|hs|elm|dart|ts|jsx|vue|angular|react|eml|msg|pst|ost|vcf|ics|cal|p7s|p7m|crt|cer|pem|key|pfx|jks|keystore)\b""",
                    RegexOption.IGNORE_CASE
                )

                val ultraFileMatches = ultraFilePattern.findAll(actualText)
                for (match in ultraFileMatches) {
                    val fileName = match.value.trim()
                    if (fileName.length > 3) {
                        foundAttachments.add(fileName)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA FILE: '$fileName'")
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 2: Any number + attachment word
                val ultraCountPatterns = listOf(
                    Regex("\\b\\d+\\s*(?:attachment|file|document|item|photo|image|video|audio)s?\\b", RegexOption.IGNORE_CASE),
                    Regex("\\b(?:attachment|file|document|item)s?\\s*:\\s*\\d+\\b", RegexOption.IGNORE_CASE),
                    Regex("\\b📎\\s*\\d*\\b"),
                    Regex("\\b\\d+\\s*(?:kb|mb|gb|bytes?)\\b", RegexOption.IGNORE_CASE)
                )

                for (pattern in ultraCountPatterns) {
                    if (pattern.containsMatchIn(actualText)) {
                        foundAttachments.add(actualText)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA COUNT: '$actualText'")
                        break
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 3: Any attachment-related word
                val ultraKeywords = listOf(
                    "attachment", "attached", "file", "document", "download", "paperclip",
                    "📎", "view", "save", "open", "preview", "drive", "photo", "image",
                    "video", "audio", "pdf", "doc", "excel", "powerpoint", "zip",
                    "archive", "media", "content", "item", "resource"
                )

                val hasUltraKeyword = ultraKeywords.any { keyword ->
                    actualText.contains(keyword, ignoreCase = true)
                }

                if (hasUltraKeyword && actualText.length < 100) {
                    // Additional validation - check if it's likely an attachment
                    val isLikelyAttachment =
                        actualText.contains(".", ignoreCase = true) ||
                        actualText.matches(Regex(".*\\d+.*", RegexOption.IGNORE_CASE)) ||
                        viewId?.contains("attachment", ignoreCase = true) == true ||
                        viewId?.contains("file", ignoreCase = true) == true ||
                        contentDesc?.contains("attachment", ignoreCase = true) == true

                    if (isLikelyAttachment) {
                        foundAttachments.add(actualText)
                        Log.d("AggressiveAttachmentScan", "✅ ULTRA KEYWORD: '$actualText'")
                    }
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 4: ViewId contains any attachment hint
                val ultraViewIdKeywords = listOf(
                    "attachment", "file", "document", "download", "paperclip", "media",
                    "photo", "image", "video", "audio", "content", "item", "resource",
                    "gmail", "compose", "email"
                )

                val hasUltraViewId = ultraViewIdKeywords.any { keyword ->
                    viewId?.contains(keyword, ignoreCase = true) == true
                }

                if (hasUltraViewId && actualText.isNotEmpty() && actualText.length < 150) {
                    foundAttachments.add("ViewId Match: $actualText")
                    Log.d("AggressiveAttachmentScan", "✅ ULTRA VIEWID: '$actualText' (ViewId: '$viewId')")
                }

                // 🎯 ULTRA-AGGRESSIVE PATTERN 5: ContentDescription hints
                val ultraContentDescKeywords = listOf(
                    "attachment", "file", "document", "download", "paperclip", "media",
                    "photo", "image", "video", "audio", "content", "item", "attached"
                )

                val hasUltraContentDesc = ultraContentDescKeywords.any { keyword ->
                    contentDesc?.contains(keyword, ignoreCase = true) == true
                }

                if (hasUltraContentDesc && actualText.isNotEmpty() && actualText.length < 150) {
                    foundAttachments.add("ContentDesc Match: $actualText")
                    Log.d("AggressiveAttachmentScan", "✅ ULTRA CONTENTDESC: '$actualText' (Desc: '$contentDesc')")
                }
            }
        }

        val validAttachments = foundAttachments
            .filter { it.isNotEmpty() && it.trim().length > 1 }
            .map { it.trim() }
            .toList()

        Log.d("AggressiveAttachmentScan", """
            📎 AGGRESSIVE ATTACHMENT SCAN COMPLETE:
            Total nodes scanned: ${nodes.size}
            Raw attachments found: ${foundAttachments.size}
            Valid attachments: ${validAttachments.size}
            Attachments: ${validAttachments.joinToString(" | ")}
        """.trimIndent())

        return validAttachments
    }

    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 15  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()

                        Log.d("checkAiResponse", "Polling attempt $retryCount: status=$status")

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }

    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI

        /*val randomNumber = (1..2).random()
        println("Random number between 1 and 2: $randomNumber")
        when (randomNumber) {
            1 -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as safe"
            }
            2 -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
                binding.tvMessage.text = "All content varified as unsafe"
            }
        }*/

        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }


    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }

    override fun onInterrupt() {

    }




}
