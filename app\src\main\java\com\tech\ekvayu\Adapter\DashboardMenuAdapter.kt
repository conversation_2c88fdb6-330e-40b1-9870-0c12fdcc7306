package com.tech.ekvayu.Adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.ItemDashboardMenuBinding
import com.tech.ekvayu.models.DashboardMenuModel


class DashboardMenuAdapter(private val items: List<DashboardMenuModel>, private val listener: onClickEventListner) : RecyclerView.Adapter<DashboardMenuAdapter.MenuViewHolder>() {

    private var selectedPosition = -1

    inner class MenuViewHolder(val binding: ItemDashboardMenuBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemDashboardMenuBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>u<PERSON>iewHolder, position: Int) {
        val item = items[position]
        holder.binding.tvMenuName.text = item.title
        holder.binding.tvDescripton.text = item.desc
        holder.binding.ivIcon.setImageResource(item.iconResId)

        // Apply theme-aware styling based on selection
        if (selectedPosition == position) {
            Log.d("GetClicked", "onBindViewHolder: clicked")
            // Selected state - use app color background
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.app_color))
            holder.binding.cvIconBackground.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.tvMenuName.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.tvDescripton.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
            holder.binding.ivIcon.setColorFilter(ContextCompat.getColor(holder.itemView.context, R.color.app_color))
        } else {
            // Normal state - use theme-aware colors
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.card_background))
            holder.binding.cvIconBackground.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.surface))
            holder.binding.tvMenuName.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.text_primary))
            holder.binding.tvDescripton.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.text_secondary))
            holder.binding.ivIcon.setColorFilter(ContextCompat.getColor(holder.itemView.context, R.color.text_primary))
        }

        holder.binding.cvMain.setOnClickListener {
            val previousPosition = selectedPosition
            selectedPosition = position

            // Notify changes for smooth animation
            if (previousPosition != -1) {
                notifyItemChanged(previousPosition)
            }
            notifyItemChanged(position)

            listener.onMenuClick(item)
        }

    }

    override fun getItemCount() = items.size


    interface  onClickEventListner{
        fun onMenuClick(item: DashboardMenuModel)
    }


}
