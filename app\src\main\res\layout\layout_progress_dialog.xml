<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="24dp"
    android:background="@drawable/bg_dialog"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_gravity="center"
    android:elevation="@dimen/_10sdp"
    app:cardElevation="@dimen/_10sdp"
    android:layout_width="wrap_content"
    app:cardCornerRadius="@dimen/_5sdp"
    android:layout_height="wrap_content">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10sdp"
        >

        <ProgressBar
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:id="@+id/progressBar"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp" />

        <androidx.appcompat.widget.AppCompatTextView
            app:layout_constraintTop_toBottomOf="@+id/progressBar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:id="@+id/tvMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Please wait..."
            android:textSize="9sp"
            android:textColor="@color/text_primary" />

    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.cardview.widget.CardView>
