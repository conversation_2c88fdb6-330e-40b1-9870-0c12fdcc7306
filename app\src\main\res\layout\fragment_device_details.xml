<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:clipToPadding="false"
    android:paddingTop="@dimen/_16sdp"
    android:paddingBottom="@dimen/_16sdp"
    tools:context=".Fragments.DeviceDetailsFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16sdp">

        <!-- Header Title -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_semi_bold"
            android:textSize="@dimen/_16sdp"
            android:textColor="@color/text_primary"
            android:text="Device Information"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Device Information Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvDeviceInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Device Details"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Device Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/device_name"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvDeviceName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Manufacturer -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/manufacturer"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvManufacturer"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Model Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/model_name"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvModelName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Brand Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/brand_name"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvBradName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Product Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/product"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvProductName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Android Information Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvAndroidInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvDeviceInfo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/android_info"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Android Version -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/android_version"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvAndroidVersion"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- API Level -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/api_level"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvApiLevel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Build Number -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/build_number"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvBuildNumber"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Serial Number -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/serial_number"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvSerialNumber"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Android ID -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/android_id"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvAndroidId"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Screen Information Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvScreenInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvAndroidInfo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/screen_info"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Resolution -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/resolution"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvResolution"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Density -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Density"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvDensity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Density Factor -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/density_factor"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvDensityFactor"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Scaled Density -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/scaled_density"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvScaleDensity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- App Information Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvAppInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            app:cardCornerRadius="@dimen/_12sdp"
            app:cardElevation="@dimen/_4sdp"
            app:cardBackgroundColor="@color/card_background"
            app:layout_constraintTop_toBottomOf="@+id/cvScreenInfo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_16sdp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/app_info"
                    android:textSize="@dimen/_12sdp"
                    android:textColor="@color/text_primary"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_marginBottom="@dimen/_12sdp" />

                <!-- Package -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Package"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvPackage"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Version -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/version"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvVersion"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Target SDK -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/_8sdp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Target SDK"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvTargetSdk"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginVertical="@dimen/_4sdp" />

                <!-- Min SDK -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/min_sdk"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_secondary"
                        android:fontFamily="@font/roboto_semi_regular" />

                    <TextView
                        android:id="@+id/tvMinSdk"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/_10sdp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="@font/roboto_semi_medium"
                        android:gravity="end" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>


    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>